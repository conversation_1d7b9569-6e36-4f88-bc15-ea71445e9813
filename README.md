# وهج بين الأزقة - رواية تفاعلية

## نظرة عامة

"وهج بين الأزقة" هي لعبة رواية مرئية تفاعلية تأخذ اللاعب في رحلة مشوقة عبر أزقة وهران العتيقة. تتبع القصة بارق، مهندس برمجيات شاب يكتشف إرث جده الغامض ويجد نفسه في قلب مؤامرة معقدة تمتد عبر التاريخ.

## الميزات الرئيسية

### 🎮 تجربة لعب تفاعلية
- قصة غنية ومتشعبة مع خيارات متعددة
- نظام علاقات متطور بين الشخصيات
- قرارات تؤثر على مجرى القصة
- نظام مزاج الشخصيات مع أيقونات تعبيرية

### 🎨 تصميم جذاب
- واجهة مستخدم عربية جميلة ومتجاوبة
- تدرجات لونية جذابة وانيميشن سلس
- صور مشاهد مخصصة لكل جزء من القصة
- تصميم متوافق مع الهواتف المحمولة

### 🔊 تجربة صوتية غامرة
- موسيقى خلفية متنوعة حسب المشاهد
- مؤثرات صوتية تفاعلية
- نظام تحكم في مستوى الصوت
- دعم كامل لإدارة الصوت

### 💾 نظام حفظ متقدم
- حفظ تلقائي للتقدم
- عدة خانات حفظ
- نظام استيراد وتصدير الحفظ
- حفظ الإعدادات والتفضيلات

### 📱 تطبيق ويب تقدمي (PWA)
- يعمل بدون اتصال بالإنترنت
- قابل للتثبيت على الهاتف
- تحديثات تلقائية
- إشعارات push

## البنية التقنية

### الملفات الأساسية
- `index.html` - الصفحة الرئيسية
- `styles.css` - التصميم والأنماط
- `game-data.js` - بيانات القصة والشخصيات
- `game-engine.js` - محرك اللعبة الأساسي
- `audio-manager.js` - إدارة الصوت
- `save-system.js` - نظام الحفظ
- `main.js` - التطبيق الرئيسي وإدارة الواجهة

### ملفات PWA
- `manifest.json` - بيانات التطبيق
- `sw.js` - Service Worker للعمل بدون اتصال

### الأصول (Assets)
- `images/` - صور المشاهد والواجهة
- `audio/` - الملفات الصوتية والموسيقى

## كيفية التشغيل

### التشغيل المحلي
1. تأكد من وجود جميع الملفات في نفس المجلد
2. افتح `index.html` في متصفح حديث
3. أو استخدم خادم محلي للحصول على تجربة أفضل

### متطلبات النظام
- متصفح حديث يدعم ES6+
- دعم للـ Service Workers
- دعم للـ Web Audio API

## المطور

**وائل شايبي** - 2025
جميع الحقوق محفوظة

---

استمتع برحلتك في "وهج بين الأزقة"! 🌟

A new Flutter project.

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.
