// Game Engine - Core game logic and mechanics
class GameEngine {
    constructor() {
        this.currentChapter = null;
        this.currentScene = null;
        this.gameData = gameData;
        this.audioManager = null;
        this.saveSystem = null;
        this.isTransitioning = false;
        this.textSpeed = 50; // milliseconds per character

        this.initializeEngine();
    }

    initializeEngine() {
        // Initialize audio manager
        this.audioManager = new AudioManager();

        // Initialize save system
        this.saveSystem = new SaveSystem();

        // Load saved game state if exists
        this.loadGameState();

        // Set up event listeners
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Handle window resize
        window.addEventListener('resize', () => {
            this.handleResize();
        });

        // Handle visibility change (for pausing audio)
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.audioManager.pauseAll();
            } else {
                this.audioManager.resumeAll();
            }
        });
    }

    // Start a new game
    startNewGame() {
        this.gameData.gameState.gameStarted = true;
        this.gameData.gameState.currentChapter = 'chapter1';
        this.gameData.gameState.currentScene = 'scene1';
        this.gameData.gameState.playerChoices = [];
        this.gameData.gameState.characterRelationships = {};

        // Initialize character relationships
        Object.keys(this.gameData.characters).forEach(charId => {
            this.gameData.gameState.characterRelationships[charId] = 0;
        });

        this.loadScene('chapter1', 'scene1');
        this.saveGameState();
    }

    // Continue existing game
    continueGame() {
        if (this.gameData.gameState.gameStarted) {
            this.loadScene(
                this.gameData.gameState.currentChapter,
                this.gameData.gameState.currentScene
            );
        } else {
            this.startNewGame();
        }
    }

    // Load a specific scene
    loadScene(chapterId, sceneId) {
        if (this.isTransitioning) return;

        const chapter = this.gameData.chapters[chapterId];
        const scene = chapter?.scenes[sceneId];

        if (!scene) {
            console.error(`Scene not found: ${chapterId}.${sceneId}`);
            return;
        }

        // Check if this is a chapter transition
        if (chapterId !== this.gameData.gameState.currentChapter) {
            this.showChapterTransition(chapterId, () => {
                this.loadSceneContent(chapterId, sceneId, chapter, scene);
            });
        } else {
            this.loadSceneContent(chapterId, sceneId, chapter, scene);
        }
    }

    // Load scene content
    loadSceneContent(chapterId, sceneId, chapter, scene) {
        this.currentChapter = chapter;
        this.currentScene = scene;

        // Update game state
        this.gameData.gameState.currentChapter = chapterId;
        this.gameData.gameState.currentScene = sceneId;

        // Update UI elements
        this.updateSceneTitle(scene.title);
        this.updateChapterInfo(chapter.number);
        this.updateSceneImage(scene.image);
        this.updateCharacterMood();

        // Load and display story text with typewriter effect
        this.displayStoryText(scene.text);

        // Load scene music
        if (scene.music) {
            this.audioManager.playBackgroundMusic(scene.music);
        }

        // Display choices after text animation
        setTimeout(() => {
            this.displayChoices(scene.choices);
        }, this.calculateTextDisplayTime(scene.text) + 500);

        // Update relationships panel
        this.updateRelationshipsPanel();

        // Auto-save if enabled
        if (this.gameData.settings.autoSave) {
            this.saveGameState();
        }
    }

    // Display story text with typewriter effect
    displayStoryText(text) {
        const storyElement = document.getElementById('storyText');
        storyElement.innerHTML = '';

        const paragraphs = text.split('\n\n');
        let totalDelay = 0;

        paragraphs.forEach((paragraph, index) => {
            if (paragraph.trim()) {
                const p = document.createElement('p');
                storyElement.appendChild(p);

                setTimeout(() => {
                    this.typewriterEffect(p, paragraph.trim(), this.getTextSpeed());
                }, totalDelay);

                totalDelay += this.calculateTextDisplayTime(paragraph) + 300;
            }
        });
    }

    // Typewriter effect for text
    typewriterEffect(element, text, speed) {
        let i = 0;
        element.innerHTML = '';

        const timer = setInterval(() => {
            if (i < text.length) {
                element.innerHTML += text.charAt(i);
                i++;
            } else {
                clearInterval(timer);
            }
        }, speed);
    }

    // Calculate text display time based on length and speed
    calculateTextDisplayTime(text) {
        return text.length * this.getTextSpeed();
    }

    // Get text speed based on settings
    getTextSpeed() {
        switch (this.gameData.settings.textSpeed) {
            case 'slow': return 80;
            case 'fast': return 20;
            default: return 50;
        }
    }

    // Display choices
    displayChoices(choices) {
        const choicesContainer = document.getElementById('choicesContainer');
        choicesContainer.innerHTML = '';

        if (!choices || choices.length === 0) return;

        choices.forEach((choice, index) => {
            const button = document.createElement('button');
            button.className = 'choice-btn';
            button.textContent = choice.text;
            button.onclick = () => this.makeChoice(choice, index);

            // Add animation delay
            button.style.animationDelay = `${index * 0.1}s`;
            button.classList.add('fade-in');

            choicesContainer.appendChild(button);
        });
    }

    // Handle player choice
    makeChoice(choice, choiceIndex) {
        try {
            // Play sound effect
            if (this.audioManager) {
                this.audioManager.playSound('click');
            }

            // Record choice
            this.gameData.gameState.playerChoices.push({
                chapter: this.gameData.gameState.currentChapter,
                scene: this.gameData.gameState.currentScene,
                choice: choiceIndex,
                text: choice.text
            });

            // Apply choice effects
            if (choice.effects) {
                this.applyChoiceEffects(choice.effects);
            }

            // Navigate to next scene
            if (choice.nextScene) {
                if (choice.nextScene.startsWith('chapter')) {
                    // Navigate to different chapter
                    this.loadScene(choice.nextScene, 'scene1');
                } else {
                    // Navigate to scene in current chapter
                    this.loadScene(this.gameData.gameState.currentChapter, choice.nextScene);
                }
            }
        } catch (error) {
            console.error('Error making choice:', error);
            // Show error to user
            if (window.gameApp) {
                window.gameApp.showNotification('حدث خطأ في معالجة الاختيار', 'error');
            }
        }
    }

    // Apply effects from player choices
    applyChoiceEffects(effects) {
        Object.keys(effects).forEach(characterId => {
            const character = this.gameData.characters[characterId];
            if (character) {
                const effect = effects[characterId];

                // Update mood
                if (effect.mood) {
                    character.mood = effect.mood;
                }

                // Update relationship
                if (effect.relationship !== undefined) {
                    if (!this.gameData.gameState.characterRelationships[characterId]) {
                        this.gameData.gameState.characterRelationships[characterId] = 0;
                    }
                    this.gameData.gameState.characterRelationships[characterId] += effect.relationship;

                    // Clamp relationship values between -10 and 10
                    this.gameData.gameState.characterRelationships[characterId] = Math.max(-10,
                        Math.min(10, this.gameData.gameState.characterRelationships[characterId]));
                }
            }
        });

        // Update UI to reflect changes
        this.updateCharacterMood();
        this.updateRelationshipsPanel();
    }

    // Update scene title
    updateSceneTitle(title) {
        const titleElement = document.getElementById('sceneTitle');
        if (titleElement) {
            titleElement.textContent = title;
        }
    }

    // Update chapter info
    updateChapterInfo(chapterNumber) {
        const chapterElement = document.getElementById('chapterNumber');
        if (chapterElement) {
            chapterElement.textContent = chapterNumber;
        }
    }

    // Update scene image
    updateSceneImage(imagePath) {
        const imageElement = document.getElementById('sceneImage');
        if (imageElement && imagePath) {
            // Add error handling for missing images
            imageElement.onerror = () => {
                console.warn(`Image not found: ${imagePath}`);
                // Use a placeholder or default image
                imageElement.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMWExYTJlIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZpbGw9IiNmZmQ3MDAiIGZvbnQtc2l6ZT0iMjQiPtmF2LTZh9ivINi62YrYsSDZhdiq2KfYrTwvdGV4dD48L3N2Zz4=';
            };

            imageElement.onload = () => {
                // Image loaded successfully
                imageElement.classList.add('fade-in');
            };

            imageElement.src = imagePath;
            imageElement.alt = 'مشهد الرواية';
        }
    }

    // Update character mood display
    updateCharacterMood() {
        const moodElement = document.getElementById('characterMood');
        if (moodElement) {
            const playerMood = this.gameData.characters.barek.mood;
            const iconClass = moodIcons[playerMood] || moodIcons.neutral;
            moodElement.innerHTML = `<i class="${iconClass}"></i>`;
        }
    }

    // Update relationships panel
    updateRelationshipsPanel() {
        const characterList = document.getElementById('characterList');
        if (!characterList) return;

        characterList.innerHTML = '';

        Object.keys(this.gameData.characters).forEach(charId => {
            if (charId === 'barek') return; // Skip player character

            const character = this.gameData.characters[charId];
            const relationship = this.gameData.gameState.characterRelationships[charId] || 0;

            const characterItem = document.createElement('div');
            characterItem.className = 'character-item';

            const relationshipText = this.getRelationshipText(relationship);
            const moodIcon = moodIcons[character.mood] || moodIcons.neutral;

            characterItem.innerHTML = `
                <div class="character-name">
                    <i class="${moodIcon}"></i>
                    ${character.name}
                </div>
                <div class="character-relationship">${relationshipText}</div>
            `;

            characterList.appendChild(characterItem);
        });
    }

    // Get relationship text based on value
    getRelationshipText(value) {
        if (value >= 8) return 'صديق مقرب';
        if (value >= 5) return 'صديق';
        if (value >= 2) return 'ودود';
        if (value >= -1) return 'محايد';
        if (value >= -4) return 'متوتر';
        if (value >= -7) return 'عدائي';
        return 'عدو';
    }

    // Show chapter transition
    showChapterTransition(chapterId, callback) {
        this.isTransitioning = true;
        const chapter = this.gameData.chapters[chapterId];

        const transitionElement = document.getElementById('chapterTransition');
        const chapterNumberElement = document.getElementById('transitionChapter');
        const chapterTitleElement = document.getElementById('transitionTitle');

        if (transitionElement && chapterNumberElement && chapterTitleElement) {
            chapterNumberElement.textContent = chapter.number;
            chapterTitleElement.textContent = chapter.title;

            transitionElement.classList.remove('hidden');

            // Store callback for continue button
            window.continueToNextChapter = () => {
                transitionElement.classList.add('hidden');
                this.isTransitioning = false;
                callback();
            };
        }
    }

    // Handle window resize
    handleResize() {
        // Adjust layout for mobile devices
        const gameContainer = document.getElementById('gameContainer');
        if (window.innerWidth <= 768) {
            gameContainer.classList.add('mobile-layout');
        } else {
            gameContainer.classList.remove('mobile-layout');
        }
    }

    // Save game state
    saveGameState() {
        if (this.saveSystem) {
            this.saveSystem.saveGame(this.gameData.gameState);
        }
    }

    // Load game state
    loadGameState() {
        if (this.saveSystem) {
            const savedState = this.saveSystem.loadGame();
            if (savedState) {
                Object.assign(this.gameData.gameState, savedState);

                // Show continue button if game was started
                if (this.gameData.gameState.gameStarted) {
                    const continueBtn = document.querySelector('.continue-btn');
                    if (continueBtn) {
                        continueBtn.style.display = 'flex';
                    }
                }
            }
        }
    }
}

// Initialize game engine when DOM is loaded
let gameEngine;
document.addEventListener('DOMContentLoaded', () => {
    gameEngine = new GameEngine();
});
