<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="menuGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1a1a2e;stop-opacity:1" />
      <stop offset="25%" style="stop-color:#16213e;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#0f3460;stop-opacity:1" />
      <stop offset="75%" style="stop-color:#16213e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1a1a2e;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="lightGradient" cx="50%" cy="30%" r="40%">
      <stop offset="0%" style="stop-color:#ffd700;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#ffd700;stop-opacity:0" />
    </radialGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#menuGradient)"/>
  <ellipse cx="600" cy="240" rx="480" ry="240" fill="url(#lightGradient)"/>
  
  <!-- Decorative elements -->
  <rect x="100" y="100" width="200" height="300" fill="#2c3e50" opacity="0.3" rx="15"/>
  <rect x="900" y="200" width="180" height="400" fill="#34495e" opacity="0.3" rx="15"/>
  <circle cx="200" cy="600" r="80" fill="#ffd700" opacity="0.2"/>
  <circle cx="1000" cy="150" r="60" fill="#ffd700" opacity="0.2"/>
  
  <!-- Arabic architectural elements -->
  <polygon points="300,400 350,350 400,400 400,500 300,500" fill="#8B4513" opacity="0.4"/>
  <polygon points="800,300 850,250 900,300 900,400 800,400" fill="#A0522D" opacity="0.4"/>
  
  <!-- Stars -->
  <circle cx="150" cy="150" r="3" fill="#ffd700" opacity="0.8"/>
  <circle cx="1050" cy="100" r="2" fill="#ffd700" opacity="0.8"/>
  <circle cx="300" cy="200" r="2" fill="#ffd700" opacity="0.8"/>
  <circle cx="900" cy="600" r="3" fill="#ffd700" opacity="0.8"/>
  <circle cx="500" cy="700" r="2" fill="#ffd700" opacity="0.8"/>
  
  <!-- Central title area -->
  <rect x="300" y="350" width="600" height="100" fill="#000000" opacity="0.5" rx="20"/>
  <text x="50%" y="52%" text-anchor="middle" fill="#ffd700" font-size="48" font-family="Arial, sans-serif" font-weight="bold">وهج بين الأزقة</text>
  <text x="50%" y="58%" text-anchor="middle" fill="#ffffff" font-size="24" font-family="Arial, sans-serif">رواية تفاعلية غامضة</text>
</svg>
