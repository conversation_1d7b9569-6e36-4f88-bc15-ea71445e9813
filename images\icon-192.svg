<svg width="192" height="192" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1a1a2e;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#16213e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0f3460;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="192" height="192" fill="url(#iconGradient)" rx="20"/>
  <circle cx="96" cy="96" r="60" fill="#ffd700" opacity="0.8"/>
  <rect x="66" y="66" width="60" height="60" fill="#2c3e50" opacity="0.9" rx="8"/>
  <text x="96" y="105" text-anchor="middle" fill="#ffd700" font-size="24" font-family="Arial, sans-serif" font-weight="bold">وهج</text>
  <circle cx="96" cy="40" r="8" fill="#ffd700" opacity="0.6"/>
  <circle cx="96" cy="152" r="8" fill="#ffd700" opacity="0.6"/>
  <circle cx="40" cy="96" r="8" fill="#ffd700" opacity="0.6"/>
  <circle cx="152" cy="96" r="8" fill="#ffd700" opacity="0.6"/>
</svg>
