<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>وهج بين الأزقة - رواية تفاعلية</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#ffd700">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="وهج بين الأزقة">
    <link rel="apple-touch-icon" href="images/icon-192.png">
    <meta name="msapplication-TileColor" content="#1a1a2e">
    <meta name="msapplication-TileImage" content="images/icon-144.png">
</head>
<body>
    <!-- Skip Link for Accessibility -->
    <a href="#main-content" class="skip-link">انتقل إلى المحتوى الرئيسي</a>

    <!-- Loading Screen -->
    <div id="loadingScreen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-logo">
                <i class="fas fa-book-open"></i>
                <h1>وهج بين الأزقة</h1>
            </div>
            <div class="loading-bar">
                <div class="loading-progress"></div>
            </div>
            <p class="loading-text">جاري تحضير الرواية...</p>
        </div>
    </div>

    <!-- Main Menu -->
    <div id="mainMenu" class="main-menu hidden">
        <div class="menu-background"></div>
        <div class="menu-content">
            <div class="game-title">
                <h1>وهج بين الأزقة</h1>
                <p class="subtitle">رواية تفاعلية غامضة</p>
            </div>
            <div class="menu-buttons">
                <button class="menu-btn start-btn" onclick="startGame()">
                    <i class="fas fa-play"></i>
                    بدء اللعبة
                </button>
                <button class="menu-btn continue-btn" onclick="continueGame()" style="display: none;">
                    <i class="fas fa-forward"></i>
                    متابعة اللعبة
                </button>
                <button class="menu-btn settings-btn" onclick="openSettings()">
                    <i class="fas fa-cog"></i>
                    الإعدادات
                </button>
                <button class="menu-btn about-btn" onclick="openAbout()">
                    <i class="fas fa-info-circle"></i>
                    حول اللعبة
                </button>
            </div>
        </div>
    </div>

    <!-- Game Container -->
    <div id="gameContainer" class="game-container hidden">
        <!-- Header -->
        <header class="game-header">
            <div class="header-left">
                <button class="hamburger-btn" onclick="toggleMenu()" aria-label="فتح القائمة" aria-expanded="false">
                    <i class="fas fa-bars" aria-hidden="true"></i>
                </button>
                <div class="chapter-info">
                    <span id="chapterNumber">الفصل الأول</span>
                </div>
            </div>
            <div class="header-center">
                <h2 id="sceneTitle">شرارة في رماد الماضي</h2>
            </div>
            <div class="header-right">
                <div class="character-mood" id="characterMood" aria-label="مزاج الشخصية" role="status">
                    <i class="fas fa-smile" aria-hidden="true"></i>
                </div>
            </div>
        </header>

        <!-- Side Menu -->
        <div id="sideMenu" class="side-menu">
            <div class="side-menu-content">
                <button class="close-menu" onclick="toggleMenu()">
                    <i class="fas fa-times"></i>
                </button>
                <ul class="menu-list">
                    <li><a href="#" onclick="openSettings()"><i class="fas fa-cog"></i> الإعدادات</a></li>
                    <li><a href="#" onclick="saveGame()"><i class="fas fa-save"></i> حفظ اللعبة</a></li>
                    <li><a href="#" onclick="loadGame()"><i class="fas fa-folder-open"></i> تحميل لعبة</a></li>
                    <li><a href="#" onclick="openAbout()"><i class="fas fa-info-circle"></i> حول اللعبة</a></li>
                    <li><a href="#" onclick="returnToMenu()"><i class="fas fa-home"></i> القائمة الرئيسية</a></li>
                </ul>
            </div>
        </div>

        <!-- Main Game Area -->
        <main id="main-content" class="game-main">
            <!-- Scene Image -->
            <div class="scene-image-container">
                <img id="sceneImage" src="images/scene1.svg" alt="مشهد الرواية" class="scene-image">
                <div class="image-overlay"></div>
            </div>

            <!-- Story Text -->
            <div class="story-container">
                <div id="storyText" class="story-text">
                    <!-- Story content will be loaded here -->
                </div>
            </div>

            <!-- Character Relationships Panel -->
            <div class="relationships-panel" id="relationshipsPanel">
                <h3>العلاقات</h3>
                <div class="character-list" id="characterList">
                    <!-- Character relationships will be displayed here -->
                </div>
            </div>

            <!-- Choices Container -->
            <div id="choicesContainer" class="choices-container">
                <!-- Choices will be dynamically added here -->
            </div>
        </main>

        <!-- Chapter Transition -->
        <div id="chapterTransition" class="chapter-transition hidden">
            <div class="transition-content">
                <h2 id="transitionChapter">الفصل الثاني</h2>
                <p id="transitionTitle">همسات من الماضي في مسجد الباشا</p>
                <button class="continue-chapter-btn" onclick="continueToNextChapter()">
                    متابعة
                </button>
            </div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div id="settingsModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3>الإعدادات</h3>
                <button class="close-modal" onclick="closeModal('settingsModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="setting-group">
                    <label for="musicVolume">مستوى الموسيقى</label>
                    <input type="range" id="musicVolume" min="0" max="100" value="70">
                    <span id="musicVolumeValue">70%</span>
                </div>
                <div class="setting-group">
                    <label for="soundVolume">مستوى المؤثرات الصوتية</label>
                    <input type="range" id="soundVolume" min="0" max="100" value="80">
                    <span id="soundVolumeValue">80%</span>
                </div>
                <div class="setting-group">
                    <label for="textSpeed">سرعة النص</label>
                    <select id="textSpeed">
                        <option value="slow">بطيء</option>
                        <option value="normal" selected>عادي</option>
                        <option value="fast">سريع</option>
                    </select>
                </div>
                <div class="setting-group">
                    <label for="autoSave">الحفظ التلقائي</label>
                    <input type="checkbox" id="autoSave" checked>
                </div>
            </div>
        </div>
    </div>

    <!-- About Modal -->
    <div id="aboutModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3>حول اللعبة</h3>
                <button class="close-modal" onclick="closeModal('aboutModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="about-content">
                    <h4>وهج بين الأزقة</h4>
                    <p>رواية تفاعلية غامضة تأخذك في رحلة مشوقة عبر أزقة وهران العتيقة، حيث تكتشف أسرار الماضي وتواجه قرارات مصيرية.</p>

                    <h4>المطور</h4>
                    <p>تطوير: <strong>وائل شايبي</strong></p>
                    <p>جميع الحقوق محفوظة © 2025</p>

                    <h4>الميزات</h4>
                    <ul>
                        <li>قصة تفاعلية غنية بالأحداث</li>
                        <li>نظام علاقات متطور بين الشخصيات</li>
                        <li>خيارات متعددة تؤثر على مجرى القصة</li>
                        <li>رسوم وموسيقى جذابة</li>
                        <li>تجربة غامرة ومشوقة</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Audio Elements -->
    <audio id="backgroundMusic" loop>
        <source src="audio/background.mp3" type="audio/mpeg">
    </audio>
    <audio id="soundEffect">
        <source src="audio/effect.mp3" type="audio/mpeg">
    </audio>

    <!-- Scripts -->
    <script src="error-handler.js"></script>
    <script src="game-data.js"></script>
    <script src="game-engine.js"></script>
    <script src="audio-manager.js"></script>
    <script src="save-system.js"></script>
    <script src="performance-optimizer.js"></script>
    <script src="main.js"></script>
</body>
</html>
