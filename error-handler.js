// Error Handler - Comprehensive error handling and debugging
class ErrorHandler {
    constructor() {
        this.errors = [];
        this.warnings = [];
        this.debugMode = this.isDebugMode();
        
        this.initializeErrorHandling();
    }

    // Check if debug mode is enabled
    isDebugMode() {
        return localStorage.getItem('wahj_debug_mode') === 'true' || 
               window.location.search.includes('debug=true') ||
               window.location.hostname === 'localhost';
    }

    // Initialize error handling
    initializeErrorHandling() {
        // Global error handler
        window.addEventListener('error', (event) => {
            this.handleError({
                type: 'JavaScript Error',
                message: event.message,
                filename: event.filename,
                line: event.lineno,
                column: event.colno,
                stack: event.error ? event.error.stack : null
            });
        });

        // Unhandled promise rejection handler
        window.addEventListener('unhandledrejection', (event) => {
            this.handleError({
                type: 'Unhandled Promise Rejection',
                message: event.reason.message || event.reason,
                stack: event.reason.stack
            });
        });

        // Console error override
        if (this.debugMode) {
            this.overrideConsole();
        }
    }

    // Handle errors
    handleError(errorInfo) {
        const error = {
            ...errorInfo,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href
        };

        this.errors.push(error);
        
        if (this.debugMode) {
            console.error('Game Error:', error);
        }

        // Show user-friendly error message
        this.showUserError(error);

        // Try to recover from error
        this.attemptRecovery(error);
    }

    // Handle warnings
    handleWarning(warningInfo) {
        const warning = {
            ...warningInfo,
            timestamp: new Date().toISOString()
        };

        this.warnings.push(warning);
        
        if (this.debugMode) {
            console.warn('Game Warning:', warning);
        }
    }

    // Show user-friendly error message
    showUserError(error) {
        let message = 'حدث خطأ غير متوقع';
        
        // Customize message based on error type
        if (error.type === 'JavaScript Error') {
            if (error.message.includes('fetch')) {
                message = 'فشل في تحميل الملفات. تحقق من الاتصال بالإنترنت.';
            } else if (error.message.includes('audio') || error.message.includes('Audio')) {
                message = 'مشكلة في تشغيل الصوت. سيتم المتابعة بدون صوت.';
            } else if (error.message.includes('localStorage')) {
                message = 'مشكلة في حفظ البيانات. تأكد من تفعيل التخزين المحلي.';
            }
        }

        // Show notification if gameApp is available
        if (window.gameApp && typeof window.gameApp.showNotification === 'function') {
            window.gameApp.showNotification(message, 'error', 5000);
        } else {
            // Fallback alert
            if (this.debugMode) {
                alert(message + '\n\nتفاصيل الخطأ: ' + error.message);
            }
        }
    }

    // Attempt to recover from errors
    attemptRecovery(error) {
        try {
            // Audio-related errors
            if (error.message.includes('audio') || error.message.includes('Audio')) {
                this.recoverAudio();
            }
            
            // Storage-related errors
            if (error.message.includes('localStorage') || error.message.includes('storage')) {
                this.recoverStorage();
            }
            
            // Network-related errors
            if (error.message.includes('fetch') || error.message.includes('network')) {
                this.recoverNetwork();
            }
            
            // Game state errors
            if (error.message.includes('gameData') || error.message.includes('gameEngine')) {
                this.recoverGameState();
            }
        } catch (recoveryError) {
            console.error('Recovery attempt failed:', recoveryError);
        }
    }

    // Recover from audio errors
    recoverAudio() {
        if (window.gameEngine && window.gameEngine.audioManager) {
            // Disable audio temporarily
            window.gameEngine.audioManager.toggleMute();
            this.handleWarning({
                type: 'Audio Recovery',
                message: 'Audio disabled due to playback issues'
            });
        }
    }

    // Recover from storage errors
    recoverStorage() {
        try {
            // Clear corrupted data
            localStorage.removeItem('wahj_bayn_alaziqah_save');
            localStorage.removeItem('wahj_bayn_alaziqah_settings');
            
            this.handleWarning({
                type: 'Storage Recovery',
                message: 'Local storage cleared due to corruption'
            });
        } catch (e) {
            console.error('Failed to clear storage:', e);
        }
    }

    // Recover from network errors
    recoverNetwork() {
        // Enable offline mode
        if (window.gameApp) {
            this.handleWarning({
                type: 'Network Recovery',
                message: 'Switched to offline mode'
            });
        }
    }

    // Recover from game state errors
    recoverGameState() {
        try {
            // Reset to initial state
            if (window.gameData) {
                window.gameData.gameState = {
                    currentChapter: 'chapter1',
                    currentScene: 'scene1',
                    playerChoices: [],
                    characterRelationships: {},
                    unlockedScenes: ['chapter1.scene1'],
                    gameStarted: false
                };
            }
            
            this.handleWarning({
                type: 'Game State Recovery',
                message: 'Game state reset to initial values'
            });
        } catch (e) {
            console.error('Failed to reset game state:', e);
        }
    }

    // Override console methods for debugging
    overrideConsole() {
        const originalError = console.error;
        const originalWarn = console.warn;
        
        console.error = (...args) => {
            originalError.apply(console, args);
            this.handleError({
                type: 'Console Error',
                message: args.join(' ')
            });
        };
        
        console.warn = (...args) => {
            originalWarn.apply(console, args);
            this.handleWarning({
                type: 'Console Warning',
                message: args.join(' ')
            });
        };
    }

    // Validate game data integrity
    validateGameData() {
        const issues = [];
        
        if (!window.gameData) {
            issues.push('gameData object is missing');
            return issues;
        }
        
        // Check characters
        if (!gameData.characters || Object.keys(gameData.characters).length === 0) {
            issues.push('No characters defined');
        }
        
        // Check chapters
        if (!gameData.chapters || Object.keys(gameData.chapters).length === 0) {
            issues.push('No chapters defined');
        } else {
            // Check each chapter
            Object.keys(gameData.chapters).forEach(chapterId => {
                const chapter = gameData.chapters[chapterId];
                if (!chapter.scenes || Object.keys(chapter.scenes).length === 0) {
                    issues.push(`Chapter ${chapterId} has no scenes`);
                }
                
                // Check each scene
                Object.keys(chapter.scenes || {}).forEach(sceneId => {
                    const scene = chapter.scenes[sceneId];
                    if (!scene.text) {
                        issues.push(`Scene ${chapterId}.${sceneId} has no text`);
                    }
                    if (!scene.choices || scene.choices.length === 0) {
                        issues.push(`Scene ${chapterId}.${sceneId} has no choices`);
                    }
                });
            });
        }
        
        // Check mood icons
        if (!window.moodIcons) {
            issues.push('moodIcons object is missing');
        }
        
        // Check sound effects
        if (!window.soundEffects) {
            issues.push('soundEffects object is missing');
        }
        
        return issues;
    }

    // Check browser compatibility
    checkCompatibility() {
        const issues = [];
        
        // Check essential features
        if (typeof Storage === 'undefined') {
            issues.push('localStorage not supported');
        }
        
        if (!('serviceWorker' in navigator)) {
            issues.push('Service Workers not supported');
        }
        
        if (!window.AudioContext && !window.webkitAudioContext) {
            issues.push('Web Audio API not supported');
        }
        
        if (!window.fetch) {
            issues.push('Fetch API not supported');
        }
        
        if (!CSS.supports('display', 'grid')) {
            issues.push('CSS Grid not supported');
        }
        
        return issues;
    }

    // Generate error report
    generateErrorReport() {
        const report = {
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href,
            errors: this.errors,
            warnings: this.warnings,
            gameDataIssues: this.validateGameData(),
            compatibilityIssues: this.checkCompatibility(),
            performance: this.getPerformanceInfo()
        };
        
        return report;
    }

    // Get performance information
    getPerformanceInfo() {
        const info = {
            memory: null,
            timing: null,
            connection: null
        };
        
        if (performance.memory) {
            info.memory = {
                used: performance.memory.usedJSHeapSize,
                total: performance.memory.totalJSHeapSize,
                limit: performance.memory.jsHeapSizeLimit
            };
        }
        
        if (performance.timing) {
            info.timing = {
                loadTime: performance.timing.loadEventEnd - performance.timing.navigationStart,
                domReady: performance.timing.domContentLoadedEventEnd - performance.timing.navigationStart
            };
        }
        
        if (navigator.connection) {
            info.connection = {
                effectiveType: navigator.connection.effectiveType,
                downlink: navigator.connection.downlink,
                rtt: navigator.connection.rtt
            };
        }
        
        return info;
    }

    // Export error report
    exportErrorReport() {
        const report = this.generateErrorReport();
        const dataStr = JSON.stringify(report, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `wahj_error_report_${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        
        URL.revokeObjectURL(link.href);
    }

    // Clear error history
    clearErrors() {
        this.errors = [];
        this.warnings = [];
    }

    // Enable debug mode
    enableDebugMode() {
        localStorage.setItem('wahj_debug_mode', 'true');
        this.debugMode = true;
        console.log('Debug mode enabled');
    }

    // Disable debug mode
    disableDebugMode() {
        localStorage.removeItem('wahj_debug_mode');
        this.debugMode = false;
        console.log('Debug mode disabled');
    }
}

// Initialize error handler
let errorHandler;
document.addEventListener('DOMContentLoaded', () => {
    errorHandler = new ErrorHandler();
    
    // Add debug controls if in debug mode
    if (errorHandler.debugMode) {
        console.log('🎮 وهج بين الأزقة - Debug Mode Active');
        console.log('Available debug commands:');
        console.log('- errorHandler.generateErrorReport() - Generate error report');
        console.log('- errorHandler.exportErrorReport() - Export error report');
        console.log('- errorHandler.validateGameData() - Validate game data');
        console.log('- errorHandler.checkCompatibility() - Check browser compatibility');
    }
});
