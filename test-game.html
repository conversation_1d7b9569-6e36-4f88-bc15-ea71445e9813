<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار وهج بين الأزقة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a2e;
            color: #ffffff;
            padding: 20px;
            direction: rtl;
        }
        .test-section {
            background: #2c3e50;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border: 2px solid #ffd700;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background: #27ae60;
            color: white;
        }
        .error {
            background: #e74c3c;
            color: white;
        }
        .warning {
            background: #f39c12;
            color: white;
        }
        button {
            background: #ffd700;
            color: #1a1a2e;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-weight: bold;
        }
        button:hover {
            background: #ffed4e;
        }
    </style>
</head>
<body>
    <h1>اختبار لعبة وهج بين الأزقة</h1>
    
    <div class="test-section">
        <h2>اختبار تحميل الملفات</h2>
        <div id="fileTests"></div>
        <button onclick="testFileLoading()">اختبار تحميل الملفات</button>
    </div>
    
    <div class="test-section">
        <h2>اختبار البيانات</h2>
        <div id="dataTests"></div>
        <button onclick="testGameData()">اختبار البيانات</button>
    </div>
    
    <div class="test-section">
        <h2>اختبار الوظائف</h2>
        <div id="functionTests"></div>
        <button onclick="testFunctions()">اختبار الوظائف</button>
    </div>
    
    <div class="test-section">
        <h2>اختبار الأداء</h2>
        <div id="performanceTests"></div>
        <button onclick="testPerformance()">اختبار الأداء</button>
    </div>
    
    <div class="test-section">
        <h2>اختبار التوافق</h2>
        <div id="compatibilityTests"></div>
        <button onclick="testCompatibility()">اختبار التوافق</button>
    </div>

    <script>
        function addResult(containerId, message, type = 'success') {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = `test-result ${type}`;
            result.textContent = message;
            container.appendChild(result);
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        async function testFileLoading() {
            clearResults('fileTests');
            
            const files = [
                'game-data.js',
                'game-engine.js',
                'audio-manager.js',
                'save-system.js',
                'main.js',
                'performance-optimizer.js',
                'styles.css',
                'manifest.json'
            ];
            
            for (const file of files) {
                try {
                    const response = await fetch(file);
                    if (response.ok) {
                        addResult('fileTests', `✓ ${file} تم تحميله بنجاح`, 'success');
                    } else {
                        addResult('fileTests', `✗ فشل في تحميل ${file}`, 'error');
                    }
                } catch (error) {
                    addResult('fileTests', `✗ خطأ في تحميل ${file}: ${error.message}`, 'error');
                }
            }
        }

        function testGameData() {
            clearResults('dataTests');
            
            // Test if gameData exists
            if (typeof gameData !== 'undefined') {
                addResult('dataTests', '✓ gameData موجود', 'success');
                
                // Test characters
                if (gameData.characters && Object.keys(gameData.characters).length > 0) {
                    addResult('dataTests', `✓ الشخصيات: ${Object.keys(gameData.characters).length}`, 'success');
                } else {
                    addResult('dataTests', '✗ لا توجد شخصيات', 'error');
                }
                
                // Test chapters
                if (gameData.chapters && Object.keys(gameData.chapters).length > 0) {
                    addResult('dataTests', `✓ الفصول: ${Object.keys(gameData.chapters).length}`, 'success');
                } else {
                    addResult('dataTests', '✗ لا توجد فصول', 'error');
                }
                
                // Test scenes
                let totalScenes = 0;
                Object.values(gameData.chapters).forEach(chapter => {
                    if (chapter.scenes) {
                        totalScenes += Object.keys(chapter.scenes).length;
                    }
                });
                addResult('dataTests', `✓ إجمالي المشاهد: ${totalScenes}`, 'success');
                
            } else {
                addResult('dataTests', '✗ gameData غير موجود', 'error');
            }
        }

        function testFunctions() {
            clearResults('functionTests');
            
            // Test global functions
            const functions = [
                'startGame',
                'continueGame',
                'openSettings',
                'openAbout',
                'toggleMenu',
                'saveGame',
                'loadGame'
            ];
            
            functions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    addResult('functionTests', `✓ ${funcName} موجودة`, 'success');
                } else {
                    addResult('functionTests', `✗ ${funcName} غير موجودة`, 'error');
                }
            });
            
            // Test classes
            const classes = [
                'GameEngine',
                'AudioManager',
                'SaveSystem',
                'GameApp',
                'PerformanceOptimizer'
            ];
            
            classes.forEach(className => {
                if (typeof window[className] === 'function') {
                    addResult('functionTests', `✓ ${className} موجودة`, 'success');
                } else {
                    addResult('functionTests', `✗ ${className} غير موجودة`, 'error');
                }
            });
        }

        function testPerformance() {
            clearResults('performanceTests');
            
            // Test memory
            if (performance.memory) {
                const memory = performance.memory;
                const usedMB = Math.round(memory.usedJSHeapSize / 1024 / 1024);
                const totalMB = Math.round(memory.totalJSHeapSize / 1024 / 1024);
                addResult('performanceTests', `✓ الذاكرة المستخدمة: ${usedMB}MB من ${totalMB}MB`, 'success');
            } else {
                addResult('performanceTests', '⚠ معلومات الذاكرة غير متاحة', 'warning');
            }
            
            // Test timing
            const startTime = performance.now();
            setTimeout(() => {
                const endTime = performance.now();
                const duration = endTime - startTime;
                if (duration < 20) {
                    addResult('performanceTests', `✓ زمن الاستجابة: ${duration.toFixed(2)}ms`, 'success');
                } else {
                    addResult('performanceTests', `⚠ زمن الاستجابة بطيء: ${duration.toFixed(2)}ms`, 'warning');
                }
            }, 10);
            
            // Test device capabilities
            const cores = navigator.hardwareConcurrency || 'غير معروف';
            const memory = navigator.deviceMemory || 'غير معروف';
            addResult('performanceTests', `✓ المعالج: ${cores} نواة`, 'success');
            addResult('performanceTests', `✓ ذاكرة الجهاز: ${memory}GB`, 'success');
        }

        function testCompatibility() {
            clearResults('compatibilityTests');
            
            // Test browser features
            const features = [
                { name: 'localStorage', test: () => typeof Storage !== 'undefined' },
                { name: 'Service Workers', test: () => 'serviceWorker' in navigator },
                { name: 'Web Audio API', test: () => 'AudioContext' in window || 'webkitAudioContext' in window },
                { name: 'Canvas', test: () => !!document.createElement('canvas').getContext },
                { name: 'CSS Grid', test: () => CSS.supports('display', 'grid') },
                { name: 'ES6 Classes', test: () => typeof class {} === 'function' },
                { name: 'Fetch API', test: () => 'fetch' in window },
                { name: 'Intersection Observer', test: () => 'IntersectionObserver' in window }
            ];
            
            features.forEach(feature => {
                try {
                    if (feature.test()) {
                        addResult('compatibilityTests', `✓ ${feature.name} مدعوم`, 'success');
                    } else {
                        addResult('compatibilityTests', `✗ ${feature.name} غير مدعوم`, 'error');
                    }
                } catch (error) {
                    addResult('compatibilityTests', `✗ خطأ في اختبار ${feature.name}`, 'error');
                }
            });
            
            // Test mobile detection
            const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            addResult('compatibilityTests', `✓ نوع الجهاز: ${isMobile ? 'هاتف محمول' : 'حاسوب'}`, 'success');
            
            // Test screen size
            const screenInfo = `${window.innerWidth}x${window.innerHeight}`;
            addResult('compatibilityTests', `✓ حجم الشاشة: ${screenInfo}`, 'success');
        }

        // Auto-run tests on load
        window.addEventListener('load', () => {
            setTimeout(() => {
                testFileLoading();
                setTimeout(() => testGameData(), 1000);
                setTimeout(() => testFunctions(), 2000);
                setTimeout(() => testPerformance(), 3000);
                setTimeout(() => testCompatibility(), 4000);
            }, 500);
        });
    </script>

    <!-- Load game scripts for testing -->
    <script src="game-data.js"></script>
    <script src="game-engine.js"></script>
    <script src="audio-manager.js"></script>
    <script src="save-system.js"></script>
    <script src="performance-optimizer.js"></script>
    <script src="main.js"></script>
</body>
</html>
