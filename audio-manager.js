// Audio Manager - Handles all audio functionality
class AudioManager {
    constructor() {
        this.backgroundMusic = null;
        this.soundEffects = new Map();
        this.musicVolume = 0.7;
        this.soundVolume = 0.8;
        this.isMuted = false;
        this.currentMusicTrack = null;

        this.initializeAudio();
    }

    initializeAudio() {
        // Get audio elements
        this.backgroundMusic = document.getElementById('backgroundMusic');
        this.soundEffect = document.getElementById('soundEffect');

        // Set initial volumes
        this.updateVolumes();

        // Preload sound effects
        this.preloadSoundEffects();

        // Handle audio loading errors
        this.setupErrorHandling();
    }

    preloadSoundEffects() {
        // Create audio objects for sound effects
        Object.keys(soundEffects).forEach(effectName => {
            const audio = new Audio();
            audio.preload = 'auto';
            audio.src = soundEffects[effectName];
            this.soundEffects.set(effectName, audio);
        });
    }

    setupErrorHandling() {
        if (this.backgroundMusic) {
            this.backgroundMusic.addEventListener('error', (e) => {
                console.warn('Background music failed to load:', e);
            });
        }

        this.soundEffects.forEach((audio, name) => {
            audio.addEventListener('error', (e) => {
                console.warn(`Sound effect '${name}' failed to load:`, e);
            });
        });
    }

    // Play background music
    playBackgroundMusic(musicPath) {
        if (!this.backgroundMusic || this.isMuted) return;

        // Don't restart the same track
        if (this.currentMusicTrack === musicPath) return;

        this.currentMusicTrack = musicPath;

        // Fade out current music
        this.fadeOut(this.backgroundMusic, 500, () => {
            this.backgroundMusic.src = musicPath;
            this.backgroundMusic.load();

            this.backgroundMusic.addEventListener('canplaythrough', () => {
                this.backgroundMusic.play().then(() => {
                    this.fadeIn(this.backgroundMusic, 1000);
                }).catch(e => {
                    console.warn('Failed to play background music:', e);
                });
            }, { once: true });
        });
    }

    // Play sound effect
    playSound(effectName) {
        if (this.isMuted) return;

        const audio = this.soundEffects.get(effectName);
        if (audio) {
            try {
                // Clone the audio to allow overlapping sounds
                const audioClone = audio.cloneNode();
                audioClone.volume = this.soundVolume;

                audioClone.play().catch(e => {
                    console.warn(`Failed to play sound effect '${effectName}':`, e);
                });
            } catch (error) {
                console.warn(`Error playing sound effect '${effectName}':`, error);
            }
        } else {
            // Create a silent audio if effect not found to prevent errors
            console.warn(`Sound effect '${effectName}' not found, creating silent audio`);
        }
    }

    // Stop background music
    stopBackgroundMusic() {
        if (this.backgroundMusic) {
            this.fadeOut(this.backgroundMusic, 500, () => {
                this.backgroundMusic.pause();
                this.backgroundMusic.currentTime = 0;
                this.currentMusicTrack = null;
            });
        }
    }

    // Pause all audio
    pauseAll() {
        if (this.backgroundMusic && !this.backgroundMusic.paused) {
            this.backgroundMusic.pause();
        }
    }

    // Resume all audio
    resumeAll() {
        if (this.backgroundMusic && this.backgroundMusic.paused && this.currentMusicTrack) {
            this.backgroundMusic.play().catch(e => {
                console.warn('Failed to resume background music:', e);
            });
        }
    }

    // Set music volume
    setMusicVolume(volume) {
        this.musicVolume = Math.max(0, Math.min(1, volume / 100));
        this.updateVolumes();

        // Update settings
        gameData.settings.musicVolume = volume;
    }

    // Set sound effects volume
    setSoundVolume(volume) {
        this.soundVolume = Math.max(0, Math.min(1, volume / 100));

        // Update settings
        gameData.settings.soundVolume = volume;
    }

    // Update volumes
    updateVolumes() {
        if (this.backgroundMusic) {
            this.backgroundMusic.volume = this.musicVolume;
        }

        this.soundEffects.forEach(audio => {
            audio.volume = this.soundVolume;
        });
    }

    // Mute/unmute all audio
    toggleMute() {
        this.isMuted = !this.isMuted;

        if (this.isMuted) {
            this.pauseAll();
        } else {
            this.resumeAll();
        }

        return this.isMuted;
    }

    // Fade in audio
    fadeIn(audio, duration) {
        if (!audio) return;

        audio.volume = 0;
        const targetVolume = audio === this.backgroundMusic ? this.musicVolume : this.soundVolume;
        const step = targetVolume / (duration / 50);

        const fadeTimer = setInterval(() => {
            if (audio.volume < targetVolume) {
                audio.volume = Math.min(targetVolume, audio.volume + step);
            } else {
                clearInterval(fadeTimer);
            }
        }, 50);
    }

    // Fade out audio
    fadeOut(audio, duration, callback) {
        if (!audio) {
            if (callback) callback();
            return;
        }

        const startVolume = audio.volume;
        const step = startVolume / (duration / 50);

        const fadeTimer = setInterval(() => {
            if (audio.volume > 0) {
                audio.volume = Math.max(0, audio.volume - step);
            } else {
                clearInterval(fadeTimer);
                if (callback) callback();
            }
        }, 50);
    }

    // Cross-fade between tracks
    crossFade(newMusicPath, duration = 1000) {
        if (!this.backgroundMusic) return;

        const oldAudio = this.backgroundMusic.cloneNode();
        oldAudio.currentTime = this.backgroundMusic.currentTime;
        oldAudio.volume = this.backgroundMusic.volume;

        // Play old audio
        oldAudio.play();

        // Fade out old audio
        this.fadeOut(oldAudio, duration / 2, () => {
            oldAudio.pause();
        });

        // Load and fade in new audio
        this.backgroundMusic.src = newMusicPath;
        this.backgroundMusic.load();

        this.backgroundMusic.addEventListener('canplaythrough', () => {
            this.backgroundMusic.play().then(() => {
                this.fadeIn(this.backgroundMusic, duration / 2);
            });
        }, { once: true });

        this.currentMusicTrack = newMusicPath;
    }

    // Get current music volume
    getMusicVolume() {
        return Math.round(this.musicVolume * 100);
    }

    // Get current sound volume
    getSoundVolume() {
        return Math.round(this.soundVolume * 100);
    }

    // Check if audio is supported
    isAudioSupported() {
        return !!(window.Audio && document.createElement('audio').canPlayType);
    }

    // Preload specific audio file
    preloadAudio(audioPath) {
        return new Promise((resolve, reject) => {
            const audio = new Audio();
            audio.preload = 'auto';

            audio.addEventListener('canplaythrough', () => {
                resolve(audio);
            }, { once: true });

            audio.addEventListener('error', (e) => {
                reject(e);
            }, { once: true });

            audio.src = audioPath;
        });
    }

    // Play ambient sound
    playAmbientSound(soundPath, loop = true, volume = 0.3) {
        const audio = new Audio(soundPath);
        audio.loop = loop;
        audio.volume = volume * this.soundVolume;

        audio.play().catch(e => {
            console.warn('Failed to play ambient sound:', e);
        });

        return audio;
    }

    // Create audio context for advanced audio processing
    createAudioContext() {
        if (window.AudioContext || window.webkitAudioContext) {
            return new (window.AudioContext || window.webkitAudioContext)();
        }
        return null;
    }

    // Apply audio effects (reverb, echo, etc.)
    applyAudioEffect(audio, effectType) {
        const audioContext = this.createAudioContext();
        if (!audioContext) return audio;

        const source = audioContext.createMediaElementSource(audio);
        const destination = audioContext.destination;

        switch (effectType) {
            case 'reverb':
                const convolver = audioContext.createConvolver();
                // Add reverb impulse response here
                source.connect(convolver);
                convolver.connect(destination);
                break;

            case 'echo':
                const delay = audioContext.createDelay();
                const feedback = audioContext.createGain();
                const wetGain = audioContext.createGain();

                delay.delayTime.value = 0.3;
                feedback.gain.value = 0.3;
                wetGain.gain.value = 0.5;

                source.connect(delay);
                delay.connect(feedback);
                feedback.connect(delay);
                delay.connect(wetGain);
                wetGain.connect(destination);
                source.connect(destination);
                break;

            default:
                source.connect(destination);
        }

        return audio;
    }

    // Cleanup audio resources
    cleanup() {
        this.stopBackgroundMusic();

        this.soundEffects.forEach(audio => {
            audio.pause();
            audio.src = '';
        });

        this.soundEffects.clear();
    }
}

// Audio utility functions
const AudioUtils = {
    // Check if audio format is supported
    isFormatSupported(format) {
        const audio = document.createElement('audio');
        return audio.canPlayType(format) !== '';
    },

    // Get optimal audio format for current browser
    getOptimalFormat() {
        const formats = ['audio/mp3', 'audio/ogg', 'audio/wav'];
        for (const format of formats) {
            if (this.isFormatSupported(format)) {
                return format;
            }
        }
        return null;
    },

    // Convert volume from linear to logarithmic scale
    linearToLog(value) {
        return Math.log(value + 0.01) / Math.log(1.01);
    },

    // Convert volume from logarithmic to linear scale
    logToLinear(value) {
        return Math.pow(1.01, value) - 0.01;
    }
};
