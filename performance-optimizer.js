// Performance Optimizer - Enhances game performance and user experience
class PerformanceOptimizer {
    constructor() {
        this.isLowEndDevice = this.detectLowEndDevice();
        this.connectionType = this.getConnectionType();
        this.prefersReducedMotion = this.checkReducedMotionPreference();
        
        this.initializeOptimizations();
    }

    // Detect if device is low-end
    detectLowEndDevice() {
        const memory = navigator.deviceMemory || 4; // Default to 4GB if not available
        const cores = navigator.hardwareConcurrency || 4; // Default to 4 cores
        const userAgent = navigator.userAgent.toLowerCase();
        
        // Consider device low-end if:
        // - Less than 2GB RAM
        // - Less than 4 CPU cores
        // - Old mobile device
        const isLowMemory = memory < 2;
        const isLowCores = cores < 4;
        const isOldMobile = /android [1-6]|iphone os [1-9]/.test(userAgent);
        
        return isLowMemory || isLowCores || isOldMobile;
    }

    // Get connection type
    getConnectionType() {
        const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
        if (!connection) return 'unknown';
        
        const type = connection.effectiveType || connection.type;
        return type;
    }

    // Check if user prefers reduced motion
    checkReducedMotionPreference() {
        return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    }

    // Initialize performance optimizations
    initializeOptimizations() {
        this.optimizeAnimations();
        this.optimizeImages();
        this.optimizeAudio();
        this.optimizeMemory();
        this.setupPerformanceMonitoring();
    }

    // Optimize animations based on device capabilities
    optimizeAnimations() {
        if (this.isLowEndDevice || this.prefersReducedMotion) {
            // Disable or reduce animations
            document.documentElement.style.setProperty('--animation-duration', '0.1s');
            document.documentElement.style.setProperty('--transition-duration', '0.1s');
            
            // Add class to disable heavy animations
            document.body.classList.add('reduced-animations');
        } else {
            // Enable full animations
            document.documentElement.style.setProperty('--animation-duration', '0.3s');
            document.documentElement.style.setProperty('--transition-duration', '0.3s');
        }
    }

    // Optimize image loading
    optimizeImages() {
        // Implement lazy loading for images
        this.setupLazyLoading();
        
        // Use appropriate image formats
        if (this.isLowEndDevice || this.connectionType === 'slow-2g' || this.connectionType === '2g') {
            // Use lower quality images for slow connections
            this.useLowQualityImages();
        }
    }

    // Setup lazy loading for images
    setupLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        if (img.dataset.src) {
                            img.src = img.dataset.src;
                            img.removeAttribute('data-src');
                            observer.unobserve(img);
                        }
                    }
                });
            });

            // Observe all images with data-src attribute
            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    }

    // Use lower quality images for slow connections
    useLowQualityImages() {
        const images = document.querySelectorAll('img');
        images.forEach(img => {
            if (img.src && img.src.includes('.svg')) {
                // SVG images are already optimized
                return;
            }
            
            // Add loading="lazy" attribute
            img.loading = 'lazy';
        });
    }

    // Optimize audio based on device and connection
    optimizeAudio() {
        if (this.isLowEndDevice || this.connectionType === 'slow-2g' || this.connectionType === '2g') {
            // Disable audio preloading
            const audioElements = document.querySelectorAll('audio');
            audioElements.forEach(audio => {
                audio.preload = 'none';
            });
            
            // Reduce audio quality if possible
            if (gameEngine && gameEngine.audioManager) {
                gameEngine.audioManager.setMusicVolume(50); // Lower default volume
            }
        }
    }

    // Optimize memory usage
    optimizeMemory() {
        // Clean up unused resources periodically
        setInterval(() => {
            this.cleanupMemory();
        }, 60000); // Every minute

        // Handle memory pressure events
        if ('memory' in performance) {
            this.monitorMemoryUsage();
        }
    }

    // Clean up memory
    cleanupMemory() {
        // Force garbage collection if available
        if (window.gc) {
            window.gc();
        }

        // Clean up unused audio objects
        if (gameEngine && gameEngine.audioManager) {
            gameEngine.audioManager.cleanup();
        }

        // Clean up old save data
        if (gameEngine && gameEngine.saveSystem) {
            gameEngine.saveSystem.cleanup();
        }
    }

    // Monitor memory usage
    monitorMemoryUsage() {
        const checkMemory = () => {
            const memInfo = performance.memory;
            const usedMemory = memInfo.usedJSHeapSize;
            const totalMemory = memInfo.totalJSHeapSize;
            const memoryUsage = (usedMemory / totalMemory) * 100;

            if (memoryUsage > 80) {
                console.warn('High memory usage detected:', memoryUsage + '%');
                this.handleHighMemoryUsage();
            }
        };

        setInterval(checkMemory, 30000); // Check every 30 seconds
    }

    // Handle high memory usage
    handleHighMemoryUsage() {
        // Reduce quality settings
        this.enableLowQualityMode();
        
        // Clean up memory
        this.cleanupMemory();
        
        // Notify user if necessary
        if (gameApp) {
            gameApp.showNotification('تم تحسين الأداء تلقائياً', 'info', 2000);
        }
    }

    // Enable low quality mode
    enableLowQualityMode() {
        document.body.classList.add('low-quality-mode');
        
        // Reduce animation quality
        document.documentElement.style.setProperty('--animation-duration', '0.1s');
        
        // Reduce audio quality
        if (gameEngine && gameEngine.audioManager) {
            gameEngine.audioManager.setMusicVolume(30);
            gameEngine.audioManager.setSoundVolume(30);
        }
    }

    // Setup performance monitoring
    setupPerformanceMonitoring() {
        // Monitor frame rate
        this.monitorFrameRate();
        
        // Monitor load times
        this.monitorLoadTimes();
        
        // Monitor user interactions
        this.monitorInteractions();
    }

    // Monitor frame rate
    monitorFrameRate() {
        let lastTime = performance.now();
        let frameCount = 0;
        let fps = 60;

        const measureFPS = (currentTime) => {
            frameCount++;
            
            if (currentTime - lastTime >= 1000) {
                fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
                frameCount = 0;
                lastTime = currentTime;
                
                // If FPS is consistently low, enable optimizations
                if (fps < 30) {
                    this.handleLowFrameRate();
                }
            }
            
            requestAnimationFrame(measureFPS);
        };

        requestAnimationFrame(measureFPS);
    }

    // Handle low frame rate
    handleLowFrameRate() {
        if (!document.body.classList.contains('low-quality-mode')) {
            console.warn('Low frame rate detected, enabling optimizations');
            this.enableLowQualityMode();
        }
    }

    // Monitor load times
    monitorLoadTimes() {
        window.addEventListener('load', () => {
            const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
            console.log('Page load time:', loadTime + 'ms');
            
            if (loadTime > 5000) { // More than 5 seconds
                console.warn('Slow page load detected');
                this.handleSlowLoad();
            }
        });
    }

    // Handle slow load
    handleSlowLoad() {
        // Enable aggressive optimizations for future loads
        localStorage.setItem('wahj_slow_connection', 'true');
        this.enableLowQualityMode();
    }

    // Monitor user interactions
    monitorInteractions() {
        let interactionCount = 0;
        const startTime = Date.now();

        ['click', 'touchstart', 'keydown'].forEach(eventType => {
            document.addEventListener(eventType, () => {
                interactionCount++;
                
                // If user is very active, ensure smooth performance
                const currentTime = Date.now();
                const timeElapsed = currentTime - startTime;
                const interactionsPerMinute = (interactionCount / timeElapsed) * 60000;
                
                if (interactionsPerMinute > 60) { // More than 1 interaction per second
                    this.optimizeForHighActivity();
                }
            });
        });
    }

    // Optimize for high user activity
    optimizeForHighActivity() {
        // Reduce background processes
        if (gameEngine && gameEngine.saveSystem) {
            gameEngine.saveSystem.stopAutoSave();
        }
        
        // Prioritize UI responsiveness
        document.body.classList.add('high-activity-mode');
    }

    // Get performance metrics
    getPerformanceMetrics() {
        return {
            isLowEndDevice: this.isLowEndDevice,
            connectionType: this.connectionType,
            prefersReducedMotion: this.prefersReducedMotion,
            memoryUsage: performance.memory ? {
                used: performance.memory.usedJSHeapSize,
                total: performance.memory.totalJSHeapSize,
                limit: performance.memory.jsHeapSizeLimit
            } : null
        };
    }

    // Apply user preferences
    applyUserPreferences() {
        // Check for saved performance preferences
        const savedPrefs = localStorage.getItem('wahj_performance_prefs');
        if (savedPrefs) {
            const prefs = JSON.parse(savedPrefs);
            
            if (prefs.lowQualityMode) {
                this.enableLowQualityMode();
            }
            
            if (prefs.reducedAnimations) {
                this.optimizeAnimations();
            }
        }
    }

    // Save performance preferences
    savePerformancePreferences(preferences) {
        localStorage.setItem('wahj_performance_prefs', JSON.stringify(preferences));
    }
}

// Initialize performance optimizer when DOM is loaded
let performanceOptimizer;
document.addEventListener('DOMContentLoaded', () => {
    performanceOptimizer = new PerformanceOptimizer();
    performanceOptimizer.applyUserPreferences();
});
