<svg width="800" height="400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient7" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#006400;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#228B22;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#32CD32;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#gradient7)"/>
  <circle cx="400" cy="200" r="80" fill="#FF69B4" opacity="0.8"/>
  <circle cx="400" cy="200" r="60" fill="#FFB6C1" opacity="0.6"/>
  <circle cx="400" cy="200" r="40" fill="#FFC0CB" opacity="0.4"/>
  <rect x="100" y="300" width="600" height="80" fill="#4682B4" opacity="0.7" rx="10"/>
  <text x="50%" y="25%" text-anchor="middle" fill="#ffd700" font-size="26" font-family="Arial, sans-serif" font-weight="bold">لغز الوردة الحجرية</text>
  <text x="50%" y="35%" text-anchor="middle" fill="#ffffff" font-size="16" font-family="Arial, sans-serif">الوردة التي لا تذبل</text>
  <text x="50%" y="85%" text-anchor="middle" fill="#ffffff" font-size="14" font-family="Arial, sans-serif">مسجد يشهد على لقاء البحر بالسماء</text>
  <text x="50%" y="92%" text-anchor="middle" fill="#cccccc" font-size="12" font-family="Arial, sans-serif">مسجد الباشا</text>
</svg>
