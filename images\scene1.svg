<svg width="800" height="400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1a1a2e;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#16213e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0f3460;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#gradient1)"/>
  <circle cx="150" cy="100" r="80" fill="#ffd700" opacity="0.3"/>
  <rect x="50" y="250" width="700" height="100" fill="#2c3e50" opacity="0.7" rx="10"/>
  <text x="50%" y="50%" text-anchor="middle" fill="#ffd700" font-size="28" font-family="Arial, sans-serif" font-weight="bold">مشهد البداية - يوم عادي يتحول إلى مغامرة</text>
  <text x="50%" y="60%" text-anchor="middle" fill="#ffffff" font-size="16" font-family="Arial, sans-serif">وهران - صباح يوم عادي</text>
</svg>
