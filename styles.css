/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', '<PERSON><PERSON>', sans-serif;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    color: #ffffff;
    overflow-x: hidden;
    direction: rtl;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #1a1a2e, #16213e, #0f3460);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% { background: linear-gradient(45deg, #1a1a2e, #16213e, #0f3460); }
    50% { background: linear-gradient(45deg, #0f3460, #1a1a2e, #16213e); }
}

.loading-content {
    text-align: center;
    animation: fadeInUp 1s ease-out;
}

.loading-logo {
    margin-bottom: 30px;
}

.loading-logo i {
    font-size: 4rem;
    color: #ffd700;
    margin-bottom: 20px;
    animation: bookOpen 2s ease-in-out infinite;
}

@keyframes bookOpen {
    0%, 100% { transform: rotateY(0deg); }
    50% { transform: rotateY(15deg); }
}

.loading-logo h1 {
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.loading-bar {
    width: 300px;
    height: 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    overflow: hidden;
    margin: 20px auto;
}

.loading-progress {
    height: 100%;
    background: linear-gradient(90deg, #ffd700, #ffed4e);
    border-radius: 3px;
    animation: loadingProgress 3s ease-in-out infinite;
}

@keyframes loadingProgress {
    0% { width: 0%; }
    100% { width: 100%; }
}

.loading-text {
    font-size: 1.1rem;
    color: #cccccc;
    animation: pulse 2s ease-in-out infinite;
}

/* Main Menu */
.main-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.menu-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('images/menu-bg.png') center/cover;
    filter: blur(2px);
    opacity: 0.3;
}

.menu-content {
    position: relative;
    text-align: center;
    z-index: 2;
    animation: fadeInUp 1s ease-out;
}

.game-title h1 {
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 10px;
    background: linear-gradient(45deg, #ffd700, #ffed4e, #ffa500);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.subtitle {
    font-size: 1.3rem;
    color: #cccccc;
    margin-bottom: 50px;
    font-weight: 300;
}

.menu-buttons {
    display: flex;
    flex-direction: column;
    gap: 20px;
    align-items: center;
}

.menu-btn {
    background: linear-gradient(45deg, #2c3e50, #34495e);
    border: 2px solid #ffd700;
    color: #ffffff;
    padding: 15px 40px;
    font-size: 1.2rem;
    font-weight: 600;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 250px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-family: 'Cairo', sans-serif;
}

.menu-btn:hover {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #1a1a2e;
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(255, 215, 0, 0.3);
}

.menu-btn i {
    font-size: 1.1rem;
}

/* Game Container */
.game-container {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
}

/* Header */
.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 25px;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    border-bottom: 2px solid #ffd700;
    position: relative;
    z-index: 100;
}

.header-left, .header-right {
    display: flex;
    align-items: center;
    gap: 15px;
    min-width: 150px;
}

.header-center {
    flex: 1;
    text-align: center;
}

.hamburger-btn {
    background: none;
    border: none;
    color: #ffd700;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 10px;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.hamburger-btn:hover {
    background: rgba(255, 215, 0, 0.2);
    transform: scale(1.1);
}

.chapter-info {
    background: linear-gradient(45deg, #2c3e50, #34495e);
    padding: 8px 15px;
    border-radius: 15px;
    border: 1px solid #ffd700;
    font-size: 0.9rem;
    font-weight: 600;
}

#sceneTitle {
    font-size: 1.5rem;
    font-weight: 600;
    color: #ffd700;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.character-mood {
    background: linear-gradient(45deg, #2c3e50, #34495e);
    padding: 10px;
    border-radius: 50%;
    border: 2px solid #ffd700;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.character-mood:hover {
    transform: scale(1.1);
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.5);
}

/* Side Menu */
.side-menu {
    position: fixed;
    top: 0;
    right: -300px;
    width: 300px;
    height: 100%;
    background: linear-gradient(135deg, #1a1a2e, #2c3e50);
    backdrop-filter: blur(10px);
    border-left: 2px solid #ffd700;
    transition: right 0.3s ease;
    z-index: 200;
}

.side-menu.open {
    right: 0;
}

.side-menu-content {
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.close-menu {
    align-self: flex-end;
    background: none;
    border: none;
    color: #ffd700;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 10px;
    margin-bottom: 30px;
    transition: all 0.3s ease;
}

.close-menu:hover {
    transform: scale(1.2);
    color: #ffed4e;
}

.menu-list {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.menu-list li a {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 20px;
    color: #ffffff;
    text-decoration: none;
    border-radius: 10px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.menu-list li a:hover {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #1a1a2e;
    transform: translateX(-5px);
}

/* Main Game Area */
.game-main {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 300px;
    grid-template-rows: 300px 1fr auto;
    gap: 20px;
    padding: 20px;
    overflow: hidden;
}

.scene-image-container {
    grid-column: 1 / -1;
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.scene-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.scene-image:hover {
    transform: scale(1.05);
}

.image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    pointer-events: none;
}

.story-container {
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 25px;
    border: 2px solid #ffd700;
    overflow-y: auto;
    max-height: 400px;
}

.story-text {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #ffffff;
    text-align: justify;
}

.story-text p {
    margin-bottom: 15px;
    animation: fadeInUp 0.8s ease-out;
}

.relationships-panel {
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    border: 2px solid #ffd700;
    overflow-y: auto;
}

.relationships-panel h3 {
    color: #ffd700;
    margin-bottom: 15px;
    text-align: center;
    font-weight: 600;
}

.character-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.character-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 10px;
    border-radius: 8px;
    border-left: 3px solid #ffd700;
    transition: all 0.3s ease;
}

.character-item:hover {
    background: rgba(255, 215, 0, 0.2);
    transform: translateX(-5px);
}

.character-name {
    font-weight: 600;
    color: #ffd700;
    margin-bottom: 5px;
}

.character-relationship {
    font-size: 0.9rem;
    color: #cccccc;
}

.choices-container {
    grid-column: 1 / -1;
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    justify-content: center;
    padding: 20px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 15px;
    border: 2px solid #ffd700;
}

.choice-btn {
    background: linear-gradient(45deg, #2c3e50, #34495e);
    border: 2px solid #ffd700;
    color: #ffffff;
    padding: 15px 25px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
    font-weight: 500;
    min-width: 200px;
    text-align: center;
    font-family: 'Cairo', sans-serif;
}

.choice-btn:hover {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #1a1a2e;
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(255, 215, 0, 0.4);
}

/* Chapter Transition */
.chapter-transition {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.transition-content {
    text-align: center;
    animation: fadeInUp 1s ease-out;
}

.transition-content h2 {
    font-size: 3rem;
    color: #ffd700;
    margin-bottom: 20px;
    font-weight: 700;
}

.transition-content p {
    font-size: 1.5rem;
    color: #cccccc;
    margin-bottom: 40px;
}

.continue-chapter-btn {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    border: none;
    color: #1a1a2e;
    padding: 15px 40px;
    font-size: 1.2rem;
    font-weight: 600;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Cairo', sans-serif;
}

.continue-chapter-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(255, 215, 0, 0.5);
}

/* Modals */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.modal-content {
    background: linear-gradient(135deg, #1a1a2e, #2c3e50);
    border-radius: 15px;
    border: 2px solid #ffd700;
    max-width: 500px;
    width: 90%;
    max-height: 80%;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from { transform: scale(0.8); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid #ffd700;
}

.modal-header h3 {
    color: #ffd700;
    font-weight: 600;
}

.close-modal {
    background: none;
    border: none;
    color: #ffd700;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.close-modal:hover {
    transform: scale(1.2);
    color: #ffed4e;
}

.modal-body {
    padding: 25px;
}

.setting-group {
    margin-bottom: 20px;
}

.setting-group label {
    display: block;
    color: #ffd700;
    margin-bottom: 8px;
    font-weight: 500;
}

.setting-group input[type="range"] {
    width: 100%;
    margin-bottom: 5px;
}

.setting-group select {
    width: 100%;
    padding: 8px;
    border-radius: 5px;
    border: 1px solid #ffd700;
    background: #2c3e50;
    color: #ffffff;
}

.about-content h4 {
    color: #ffd700;
    margin-bottom: 10px;
    margin-top: 20px;
}

.about-content p {
    margin-bottom: 10px;
    line-height: 1.6;
}

.about-content ul {
    margin-right: 20px;
}

.about-content li {
    margin-bottom: 5px;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

.fade-out {
    animation: fadeOut 0.5s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(45deg, #2c3e50, #34495e);
    border: 2px solid #ffd700;
    border-radius: 10px;
    padding: 15px 20px;
    color: #ffffff;
    z-index: 3000;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    max-width: 300px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.notification.show {
    transform: translateX(0);
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.notification-success {
    border-color: #27ae60;
}

.notification-error {
    border-color: #e74c3c;
}

.notification-info {
    border-color: #3498db;
}

/* Advanced Animations */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

@keyframes bounceIn {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }
    50% {
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(255, 215, 0, 0.8);
    }
}

/* Enhanced Choice Buttons */
.choice-btn {
    position: relative;
    overflow: hidden;
}

.choice-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.choice-btn:hover::before {
    left: 100%;
}

.choice-btn:active {
    transform: translateY(-1px);
}

/* Character Mood Animations */
.character-mood {
    animation: moodPulse 3s ease-in-out infinite;
}

@keyframes moodPulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

/* Story Text Enhancements */
.story-text p {
    position: relative;
    padding-left: 20px;
}

.story-text p::before {
    content: '◆';
    position: absolute;
    left: 0;
    color: #ffd700;
    font-weight: bold;
}

.story-text p:first-child::before {
    content: '◇';
}

/* Loading Enhancements */
.loading-content {
    position: relative;
}

.loading-content::before {
    content: '';
    position: absolute;
    top: -50px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 100px;
    border: 3px solid transparent;
    border-top: 3px solid #ffd700;
    border-radius: 50%;
    animation: spin 2s linear infinite;
}

@keyframes spin {
    0% { transform: translateX(-50%) rotate(0deg); }
    100% { transform: translateX(-50%) rotate(360deg); }
}

/* Mobile Optimizations */
.mobile-layout .game-main {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto auto;
    gap: 15px;
}

.mobile-layout .scene-image-container {
    height: 200px;
}

.mobile-layout .story-container {
    max-height: 300px;
}

.mobile-layout .relationships-panel {
    max-height: 200px;
}

/* Dark Mode Enhancements */
@media (prefers-color-scheme: dark) {
    .story-text {
        color: #e8e8e8;
    }

    .character-item {
        background: rgba(255, 255, 255, 0.05);
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .menu-btn,
    .choice-btn {
        border-width: 3px;
    }

    .story-text {
        font-weight: 600;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Print Styles */
@media print {
    .game-header,
    .side-menu,
    .choices-container,
    .modal {
        display: none !important;
    }

    .story-container {
        background: white !important;
        color: black !important;
        border: none !important;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .game-main {
        grid-template-columns: 1fr;
        grid-template-rows: 200px auto auto auto;
    }

    .relationships-panel {
        order: 3;
    }

    .choices-container {
        order: 4;
    }

    .game-title h1 {
        font-size: 2.5rem;
    }

    .menu-btn {
        min-width: 200px;
        padding: 12px 30px;
        font-size: 1rem;
    }

    .side-menu {
        width: 250px;
        right: -250px;
    }

    .notification {
        right: 10px;
        left: 10px;
        max-width: none;
        transform: translateY(-100px);
    }

    .notification.show {
        transform: translateY(0);
    }
}

@media (max-width: 480px) {
    .game-header {
        padding: 10px 15px;
    }

    #sceneTitle {
        font-size: 1.2rem;
    }

    .game-main {
        padding: 15px;
        gap: 15px;
    }

    .story-container {
        padding: 20px;
    }

    .choice-btn {
        min-width: 150px;
        padding: 12px 20px;
        font-size: 0.9rem;
    }

    .game-title h1 {
        font-size: 2rem;
    }

    .loading-logo h1 {
        font-size: 2rem;
    }
}

/* Performance Optimizations */
.reduced-animations * {
    animation-duration: 0.1s !important;
    transition-duration: 0.1s !important;
}

.low-quality-mode .scene-image {
    image-rendering: pixelated;
    filter: contrast(0.9);
}

.low-quality-mode .loading-content::before {
    display: none;
}

.high-activity-mode .choice-btn:hover::before {
    display: none;
}

/* CSS Variables for Dynamic Optimization */
:root {
    --animation-duration: 0.3s;
    --transition-duration: 0.3s;
}

.choice-btn {
    transition: all var(--transition-duration) ease;
}

.modal-content {
    animation-duration: var(--animation-duration);
}

/* Accessibility Improvements */
@media (prefers-contrast: high) {
    .story-text {
        background: #000000 !important;
        color: #ffffff !important;
    }

    .choice-btn {
        border-width: 3px !important;
        font-weight: bold !important;
    }
}

/* Focus indicators for keyboard navigation */
.choice-btn:focus,
.menu-btn:focus,
.hamburger-btn:focus {
    outline: 3px solid #ffd700;
    outline-offset: 2px;
}

/* Skip link for screen readers */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #ffd700;
    color: #1a1a2e;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 10000;
}

.skip-link:focus {
    top: 6px;
}
